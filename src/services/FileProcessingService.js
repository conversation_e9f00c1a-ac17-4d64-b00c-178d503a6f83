import * as fs from 'fs/promises';
import * as path from 'path';
import { PDFDocument } from 'pdf-lib';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import sharp from 'sharp';
import { from<PERSON>uffer } from 'pdf2pic';
import logger from '../config/logger.js';
import { S3Service } from './S3Service.js';
import { LLMService } from './LLMService.js';
import { PineconeService } from './PineconeService.js';

/**
 * @typedef {Object} ProcessedFile
 * @property {'image'|'document'|'text'} type - File type
 * @property {string|Array} content - File content
 * @property {Object} metadata - File metadata
 * @property {string} metadata.originalName - Original filename
 * @property {string} metadata.mimeType - MIME type
 * @property {number} metadata.size - File size in bytes
 * @property {Date} metadata.processedAt - Processing timestamp
 * @property {string} [filePath] - Path where file is stored (local path or S3 URL)
 * @property {string} [s3Url] - S3 URL if file is stored in S3
 * @property {string} [s3Key] - S3 key if file is stored in S3
 */

/**
 * @typedef {Object} FileAttachment
 * @property {Buffer} buffer - File buffer
 * @property {string} originalname - Original filename
 * @property {string} mimetype - MIME type
 * @property {number} size - File size in bytes
 */

export class FileProcessingService {
  static MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  static SUPPORTED_IMAGE_TYPES = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp'
  ];
  static SUPPORTED_DOCUMENT_TYPES = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'text/plain',
    'text/markdown',
    'application/json'
  ];

  /**
   * Save file to S3 or local storage and return the file path/URL
   * @param {FileAttachment} file - File to save
   * @param {string} [subfolder] - Optional subfolder for organization
   * @returns {Promise<Object>} File storage result
   */
  static async saveFile(file, subfolder = 'attachments') {
    // Try S3 first if available
    if (S3Service.isAvailable()) {
      try {
        const s3Result = await S3Service.uploadFile(
          file.buffer,
          file.originalname,
          file.mimetype,
          subfolder
        );

        // Store secure file mapping
        S3Service.storeSecureFileMapping(
          s3Result.secureFileId,
          s3Result.s3Key,
          file.originalname,
          file.mimetype
        );

        return {
          filePath: s3Result.s3Url,
          s3Url: s3Result.s3Url,
          s3Key: s3Result.s3Key,
          secureFileId: s3Result.secureFileId,
          storageType: 's3'
        };
      } catch (error) {
        logger.error('Failed to upload to S3, falling back to local storage:', error);
        // Fall through to local storage
      }
    }

    // Fallback to local storage
    return await this.saveFileToLocalStorage(file);
  }

  /**
   * Save file to local uploads directory (fallback method)
   * @param {FileAttachment} file - File to save
   * @returns {Promise<Object>} File storage result
   */
  static async saveFileToLocalStorage(file) {
    const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');

    // Ensure uploads directory exists
    await fs.mkdir(uploadsDir, { recursive: true });

    // Generate unique filename
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const fileExtension = path.extname(file.originalname);
    const fileName = `${timestamp}_${randomSuffix}${fileExtension}`;
    const filePath = path.join(uploadsDir, fileName);

    // Save file to disk
    await fs.writeFile(filePath, file.buffer);

    // Return relative path from project root
    const relativePath = path.join('uploads', 'chatAttachments', fileName);

    // Generate secure file ID for local files too using existing variables
    const baseName = path.basename(file.originalname, fileExtension);
    const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9-_]/g, '_');
    const secureFileId = `${timestamp}_${randomSuffix}_${sanitizedBaseName}${fileExtension}`;

    return {
      filePath: relativePath,
      s3Url: null,
      s3Key: null,
      secureFileId,
      storageType: 'local'
    };
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use saveFile() instead
   * @param {FileAttachment} file - File to save
   * @returns {Promise<string>} File path
   */
  static async saveFileToUploads(file) {
    const result = await this.saveFile(file);
    return result.filePath;
  }

  /**
   * Validate file before processing
   * @param {FileAttachment} file - File to validate
   * @throws {Error} If file is invalid
   */
  static validateFile(file) {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      throw new Error(`File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    // Check if file type is supported
    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype);
    const isDocument = this.SUPPORTED_DOCUMENT_TYPES.includes(file.mimetype);
    const isVideo = file.mimetype.startsWith('video/');

    if (isVideo) {
      throw new Error('Video files are not supported');
    }

    if (!isImage && !isDocument) {
      throw new Error(`Unsupported file type: ${file.mimetype}`);
    }

    logger.info(`File validation passed: ${file.originalname} (${file.mimetype}, ${file.size} bytes)`);
  }

  /**
   * Process file based on its type with content extraction and Pinecone storage
   * @param {FileAttachment} file - File to process
   * @param {string} [subfolder] - Optional subfolder for organization
   * @param {string} [sessionId] - Session ID for Pinecone namespace
   * @returns {Promise<ProcessedFile>} Processed file data
   */
  static async processFile(file, subfolder = 'attachments', sessionId = null) {
    this.validateFile(file);

    // Save file to S3 or local storage
    const fileStorage = await this.saveFile(file, subfolder);

    const metadata = {
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      processedAt: new Date(),
      storageType: fileStorage.storageType,
      s3Url: fileStorage.s3Url,
      s3Key: fileStorage.s3Key,
      secureFileId: fileStorage.secureFileId
    };

    try {
      let processedFile;

      // Process images
      if (this.SUPPORTED_IMAGE_TYPES.includes(file.mimetype)) {
        processedFile = await this.processImage(file, metadata, fileStorage.filePath);
      }
      // Process PDF
      else if (file.mimetype === 'application/pdf') {
        processedFile = await this.processPDFWithContentExtraction(file, metadata, fileStorage.filePath, sessionId);
      }
      // Process Word documents
      else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
        processedFile = await this.processWordDocument(file, metadata, fileStorage.filePath);
      }
      // Process Excel files
      else if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
        processedFile = await this.processExcelFile(file, metadata, fileStorage.filePath);
      }
      // Process text files
      else if (file.mimetype.startsWith('text/') || file.mimetype === 'application/json') {
        processedFile = await this.processTextFile(file, metadata, fileStorage.filePath);
      }
      else {
        throw new Error(`Unsupported file type: ${file.mimetype}`);
      }

      // Store content in Pinecone if sessionId is provided and content is available
      if (sessionId && processedFile.extractedContent) {
        await this.storeContentInPinecone(sessionId, processedFile, metadata);
      }

      return processedFile;
    } catch (error) {
      logger.error(`Error processing file ${file.originalname}:`, error);
      throw new Error(`Failed to process file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process image files
   * @param {FileAttachment} file - Image file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed image
   */
  static async processImage(file, metadata, filePath) {
    try {
      // Optimize image if needed (compress large images)
      let processedBuffer = file.buffer;
      
      if (file.size > 1024 * 1024) { // If larger than 1MB, compress
        processedBuffer = await sharp(file.buffer)
          .jpeg({ quality: 85 })
          .toBuffer();
        
        logger.info(`Compressed image ${file.originalname} from ${file.size} to ${processedBuffer.length} bytes`);
      }

      const base64Image = processedBuffer.toString('base64');
      const mimeType = file.mimetype === 'image/jpg' ? 'image/jpeg' : file.mimetype;
      
      return {
        type: 'image',
        content: [{
          type: 'image_url',
          image_url: {
            url: `data:${mimeType};base64,${base64Image}`
          }
        }],
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing image ${file.originalname}:`, error);
      throw new Error(`Failed to process image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process PDF files with content extraction using image conversion and LLM
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @param {string} [sessionId] - Session ID for content storage
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDFWithContentExtraction(file, metadata, filePath, sessionId = null) {
    try {
      // Load PDF document to get page count
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      logger.info(`Processing PDF: ${file.originalname} with ${pageCount} pages`);

      // Convert PDF to images
      const images = await this.convertPDFToImages(file.buffer);

      // Extract text content from images using LLM
      const extractedContent = await this.extractTextFromPDFImages(images, file.originalname);

      return {
        type: 'document',
        content: `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nExtracted Content:\n${extractedContent}`,
        extractedContent: extractedContent, // Raw extracted content for Pinecone storage
        metadata: {
          ...metadata,
          pageCount,
          hasExtractedContent: true
        },
        filePath
      };
    } catch (error) {
      logger.error(`Error processing PDF ${file.originalname}:`, error);

      // Fallback to basic PDF processing
      return await this.processPDFBasic(file, metadata, filePath);
    }
  }

  /**
   * Convert PDF buffer to images
   * @param {Buffer} pdfBuffer - PDF file buffer
   * @returns {Promise<Array>} Array of image buffers
   */
  static async convertPDFToImages(pdfBuffer) {
    try {
      // Create temporary directory for output
      const tempDir = path.join(process.cwd(), 'temp');
      await fs.mkdir(tempDir, { recursive: true });

      // Configure pdf2pic options
      const options = {
        density: 150,           // DPI for good text extraction quality
        saveFilename: `pdf_page_${Date.now()}`,
        savePath: tempDir,
        format: 'jpeg',
        width: 1024,           // Good resolution for text extraction
        height: 1024,
        preserveAspectRatio: true
      };

      // Initialize pdf2pic converter
      const convert = fromBuffer(pdfBuffer, options);

      // Convert all pages (-1 means all pages)
      const results = await convert.bulk(-1, { responseType: 'buffer' });

      // Extract image buffers from results
      const images = [];
      for (const result of results) {
        if (result.buffer) {
          images.push(result.buffer);
        }
      }

      logger.info(`Converted PDF to ${images.length} images using pdf2pic`);
      return images;
    } catch (error) {
      logger.error('Error converting PDF to images:', error);
      throw error;
    }
  }

  /**
   * Extract text content from PDF images using LLM
   * @param {Array} images - Array of image buffers
   * @param {string} fileName - Original file name
   * @returns {Promise<string>} Extracted text content
   */
  static async extractTextFromPDFImages(images, fileName) {
    try {
      let extractedText = '';

      for (let i = 0; i < images.length; i++) {
        const imageBuffer = images[i];

        // Convert image buffer to base64
        const base64Image = imageBuffer.toString('base64');
        const imageContent = {
          type: 'image_url',
          image_url: {
            url: `data:image/jpeg;base64,${base64Image}`
          }
        };

        // Use LLM to extract text from image
        const prompt = `Extract all text content from this image. Return only the text content without any additional commentary or formatting. If there are tables, preserve their structure using plain text formatting.`;

        try {
          const pageText = await LLMService.generateResponse(
            prompt,
            'gpt-4o-mini', // Use vision-capable model
            'You are a text extraction assistant. Extract text accurately from images.',
            [],
            {
              type: 'image',
              content: [imageContent],
              metadata: { originalName: fileName }
            }
          );

          extractedText += `\n--- Page ${i + 1} ---\n${pageText}\n`;
        } catch (pageError) {
          logger.warn(`Failed to extract text from page ${i + 1} of ${fileName}:`, pageError);
          extractedText += `\n--- Page ${i + 1} ---\n[Text extraction failed for this page]\n`;
        }
      }

      logger.info(`Extracted text from ${images.length} pages of ${fileName}`);
      return extractedText.trim();
    } catch (error) {
      logger.error('Error extracting text from PDF images:', error);
      throw error;
    }
  }

  /**
   * Basic PDF processing fallback
   * @param {FileAttachment} file - PDF file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed PDF
   */
  static async processPDFBasic(file, metadata, filePath) {
    try {
      const pdfDoc = await PDFDocument.load(file.buffer);
      const pageCount = pdfDoc.getPageCount();

      const textContent = `PDF Document: ${file.originalname}\nPages: ${pageCount}\n\nThis PDF has been uploaded and saved. The LLM can analyze the document structure and content.`;

      return {
        type: 'document',
        content: textContent,
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error in basic PDF processing ${file.originalname}:`, error);
      throw error;
    }
  }

  /**
   * Process Word documents
   * @param {FileAttachment} file - Word document
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed document
   */
  static async processWordDocument(file, metadata, filePath) {
    try {
      const result = await mammoth.convertToHtml({ buffer: file.buffer });

      if (result.messages.length > 0) {
        logger.warn(`Word document conversion warnings for ${file.originalname}:`, result.messages);
      }

      // Convert HTML to basic markdown-like format
      let markdownContent = result.value
        .replace(/<h([1-6])>/g, (match, level) => '#'.repeat(parseInt(level)) + ' ')
        .replace(/<\/h[1-6]>/g, '\n\n')
        .replace(/<p>/g, '')
        .replace(/<\/p>/g, '\n\n')
        .replace(/<strong>/g, '**')
        .replace(/<\/strong>/g, '**')
        .replace(/<em>/g, '*')
        .replace(/<\/em>/g, '*')
        .replace(/<br\s*\/?>/g, '\n')
        .replace(/<[^>]*>/g, '') // Remove remaining HTML tags
        .replace(/\n{3,}/g, '\n\n') // Clean up excessive newlines
        .trim();

      return {
        type: 'document',
        content: markdownContent,
        extractedContent: markdownContent, // For Pinecone storage
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Word document ${file.originalname}:`, error);
      throw new Error(`Failed to process Word document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process Excel files
   * @param {FileAttachment} file - Excel file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed spreadsheet
   */
  static async processExcelFile(file, metadata, filePath) {
    try {
      const workbook = XLSX.read(file.buffer, { type: 'buffer' });
      let markdownContent = '';

      workbook.SheetNames.forEach((sheetName, index) => {
        const worksheet = workbook.Sheets[sheetName];
        const csvData = XLSX.utils.sheet_to_csv(worksheet);

        markdownContent += `## Sheet: ${sheetName}\n\n`;

        // Convert CSV to markdown table
        const lines = csvData.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
          // Header row
          const headers = lines[0].split(',').map(h => h.trim());
          markdownContent += `| ${headers.join(' | ')} |\n`;
          markdownContent += `| ${headers.map(() => '---').join(' | ')} |\n`;

          // Data rows (limit to first 50 rows to avoid token limits)
          const dataRows = lines.slice(1, 51);
          dataRows.forEach(line => {
            const cells = line.split(',').map(c => c.trim());
            markdownContent += `| ${cells.join(' | ')} |\n`;
          });

          if (lines.length > 51) {
            markdownContent += `\n*... and ${lines.length - 51} more rows*\n`;
          }
        }

        markdownContent += '\n';
      });

      return {
        type: 'document',
        content: markdownContent,
        extractedContent: markdownContent, // For Pinecone storage
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing Excel file ${file.originalname}:`, error);
      throw new Error(`Failed to process Excel file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process text files
   * @param {FileAttachment} file - Text file
   * @param {Object} metadata - File metadata
   * @param {string} filePath - File path
   * @returns {Promise<ProcessedFile>} Processed text
   */
  static async processTextFile(file, metadata, filePath) {
    try {
      const content = file.buffer.toString('utf-8');

      return {
        type: 'text',
        content,
        extractedContent: content, // For Pinecone storage
        metadata,
        filePath
      };
    } catch (error) {
      logger.error(`Error processing text file ${file.originalname}:`, error);
      throw new Error(`Failed to process text file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Store extracted content in Pinecone
   * @param {string} sessionId - Session ID for namespace
   * @param {ProcessedFile} processedFile - Processed file with extracted content
   * @param {Object} metadata - File metadata
   * @returns {Promise<void>}
   */
  static async storeContentInPinecone(sessionId, processedFile, metadata) {
    try {
      if (!processedFile.extractedContent) {
        logger.warn('No extracted content to store in Pinecone');
        return;
      }

      const fileId = metadata.secureFileId || `file_${Date.now()}`;

      await PineconeService.storeFileContent(
        sessionId,
        fileId,
        processedFile.extractedContent,
        {
          originalFileName: metadata.originalName,
          mimeType: metadata.mimeType,
          fileType: processedFile.type,
          storageType: metadata.storageType
        }
      );

      logger.info(`Stored content for file ${metadata.originalName} in Pinecone namespace ${sessionId}`);
    } catch (error) {
      logger.error('Error storing content in Pinecone:', error);
      // Don't throw error to avoid breaking the file processing flow
    }
  }

  /**
   * Get system prompt addition based on file type
   * @param {ProcessedFile} processedFile - Processed file
   * @returns {string} System prompt addition
   */
  static getSystemPromptAddition(processedFile) {
    const { type, metadata } = processedFile;

    switch (type) {
      case 'image':
        return `\n\nThe user has attached an image file (${metadata.originalName}). Please analyze the image content and respond accordingly.`;

      case 'document':
        if (metadata.mimeType === 'application/pdf') {
          return `\n\nThe user has attached a PDF document (${metadata.originalName}) which has been converted to images. Please analyze the document content and respond accordingly.`;
        } else if (metadata.mimeType.includes('word')) {
          return `\n\nThe user has attached a Word document (${metadata.originalName}) with the following content converted to markdown format. Please analyze the document and respond accordingly.`;
        } else if (metadata.mimeType.includes('sheet')) {
          return `\n\nThe user has attached an Excel spreadsheet (${metadata.originalName}) with the following data converted to markdown table format. Please analyze the data and respond accordingly.`;
        }
        return `\n\nThe user has attached a document (${metadata.originalName}). Please analyze the content and respond accordingly.`;

      case 'text':
        return `\n\nThe user has attached a text file (${metadata.originalName}) with the following content. Please analyze the text and respond accordingly.`;

      default:
        return `\n\nThe user has attached a file (${metadata.originalName}). Please analyze the content and respond accordingly.`;
    }
  }

  /**
   * Clean up old uploaded files (both S3 and local)
   * @param {number} [maxAgeHours] - Maximum age in hours
   * @returns {Promise<number>} Number of files deleted
   */
  static async cleanupOldFiles(maxAgeHours = 24) {
    let totalDeleted = 0;

    try {
      // Clean up S3 files if S3 is available
      if (S3Service.isAvailable()) {
        const s3Deleted = await S3Service.cleanupOldFiles(maxAgeHours);
        totalDeleted += s3Deleted;
      }

      // Clean up local files
      try {
        const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');
        const files = await fs.readdir(uploadsDir);
        const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);

        let localDeleted = 0;

        for (const file of files) {
          const filePath = path.join(uploadsDir, file);
          const stats = await fs.stat(filePath);

          if (stats.mtime.getTime() < cutoffTime) {
            await fs.unlink(filePath);
            localDeleted++;
          }
        }

        totalDeleted += localDeleted;

        if (localDeleted > 0) {
          logger.info(`Cleaned up ${localDeleted} old local uploaded files`);
        }
      } catch (localError) {
        // Directory might not exist, which is fine
        if (localError.code !== 'ENOENT') {
          logger.error('Error cleaning up local files:', localError);
        }
      }

      if (totalDeleted > 0) {
        logger.info(`Total files cleaned up: ${totalDeleted}`);
      }

      return totalDeleted;
    } catch (error) {
      logger.error('Error during file cleanup:', error);
      return totalDeleted;
    }
  }

  /**
   * Get file processing statistics (both S3 and local)
   * @returns {Promise<Object>} Processing statistics
   */
  static async getProcessingStats() {
    try {
      const stats = {
        local: {
          totalFiles: 0,
          totalSizeBytes: 0,
          totalSizeMB: 0,
        },
        s3: {
          available: S3Service.isAvailable(),
          bucket: S3Service.bucketName,
          region: S3Service.region,
        },
        combined: {
          totalFiles: 0,
          totalSizeBytes: 0,
          totalSizeMB: 0,
        }
      };

      // Get local file statistics
      try {
        const uploadsDir = path.join(process.cwd(), 'uploads', 'chatAttachments');
        const files = await fs.readdir(uploadsDir);
        let totalSize = 0;

        for (const file of files) {
          const filePath = path.join(uploadsDir, file);
          const fileStats = await fs.stat(filePath);
          totalSize += fileStats.size;
        }

        stats.local = {
          totalFiles: files.length,
          totalSizeBytes: totalSize,
          totalSizeMB: Math.round(totalSize / (1024 * 1024) * 100) / 100,
        };
      } catch (error) {
        // Directory doesn't exist yet, keep defaults
      }

      // Note: S3 statistics would require listing objects which can be expensive
      // In production, consider using CloudWatch metrics or S3 inventory reports

      stats.combined = {
        totalFiles: stats.local.totalFiles,
        totalSizeBytes: stats.local.totalSizeBytes,
        totalSizeMB: stats.local.totalSizeMB,
      };

      return stats;
    } catch (error) {
      logger.error('Error getting processing stats:', error);
      throw error;
    }
  }

  /**
   * Validate file type and size
   * @param {string} mimetype - File MIME type
   * @param {number} size - File size in bytes
   * @returns {Object} Validation result
   */
  static validateFileType(mimetype, size) {
    const errors = [];

    if (size > this.MAX_FILE_SIZE) {
      errors.push(`File size exceeds maximum limit of ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    const isImage = this.SUPPORTED_IMAGE_TYPES.includes(mimetype);
    const isDocument = this.SUPPORTED_DOCUMENT_TYPES.includes(mimetype);
    const isVideo = mimetype.startsWith('video/');

    if (isVideo) {
      errors.push('Video files are not supported');
    }

    if (!isImage && !isDocument) {
      errors.push(`Unsupported file type: ${mimetype}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      fileType: isImage ? 'image' : isDocument ? 'document' : 'unknown',
    };
  }
}

