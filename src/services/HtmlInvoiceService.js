import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import logger from '../config/logger.js';
import { S3Service } from './S3Service.js';
import { LogoUtils } from '../utils/logoUtils.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * HTML-based Invoice Service
 * Alternative to PDF-lib for better invoice design
 * Note: This requires a HTML-to-PDF library like puppeteer or html-pdf-node
 */
export class HtmlInvoiceService {
  static invoicesDir = path.join(process.cwd(), 'storage', 'invoices');
  static templatePath = path.join(process.cwd(), 'src', 'templates', 'invoice', 'invoice.html');
  static invoiceMetadataFile = path.join(process.cwd(), 'storage', 'invoice-metadata.json');
  static invoiceMetadata = new Map();

  /**
   * Initialize the service
   */
  static async initialize() {
    try {
      // Ensure invoices directory exists
      await fs.mkdir(this.invoicesDir, { recursive: true });

      // Load existing metadata
      await this.loadMetadata();

      logger.info('HtmlInvoiceService initialized');
    } catch (error) {
      logger.error('Error initializing HtmlInvoiceService:', error);
    }
  }

  /**
   * Load invoice metadata from file
   */
  static async loadMetadata() {
    try {
      const data = await fs.readFile(this.invoiceMetadataFile, 'utf8');
      const metadata = JSON.parse(data);
      this.invoiceMetadata = new Map(Object.entries(metadata));
    } catch (error) {
      // File doesn't exist or is invalid, start with empty metadata
      this.invoiceMetadata = new Map();
    }
  }

  /**
   * Save invoice metadata to file
   */
  static async saveMetadata() {
    try {
      const metadata = Object.fromEntries(this.invoiceMetadata);
      await fs.writeFile(this.invoiceMetadataFile, JSON.stringify(metadata, null, 2));
    } catch (error) {
      logger.error('Error saving invoice metadata:', error);
    }
  }

  /**
   * Generate HTML invoice
   * @param {Object} invoiceData - Invoice data
   * @returns {Promise<string>} Generated HTML
   */
  static async generateHtmlInvoice(invoiceData) {
    try {
      // Read template
      const template = await fs.readFile(this.templatePath, 'utf8');

      // Get logo as base64
      const logoBase64 = await LogoUtils.getPngLogoAsBase64();

      // Prepare template data
      const templateData = {
        ...invoiceData,
        date: this.formatDate(invoiceData.date),
        dueDate: this.formatDate(invoiceData.dueDate),
        paidAt: this.formatDate(invoiceData.paidAt),
        nextBillingDate: invoiceData.nextBillingDate ? this.formatDate(invoiceData.nextBillingDate) : null,
        logoBase64: logoBase64 || '', // Include logo base64 data
      };

      // Simple template replacement (for a production app, use a proper template engine like Handlebars)
      let html = template;
      
      // Replace simple variables
      html = html.replace(/\{\{(\w+(?:\.\w+)*)\}\}/g, (match, path) => {
        return this.getNestedValue(templateData, path) || '';
      });

      // Handle items loop (simplified)
      const itemsMatch = html.match(/\{\{#each items\}\}([\s\S]*?)\{\{\/each\}\}/);
      if (itemsMatch && invoiceData.items) {
        const itemTemplate = itemsMatch[1];
        const itemsHtml = invoiceData.items.map(item => {
          let itemHtml = itemTemplate;
          itemHtml = itemHtml.replace(/\{\{description\}\}/g, item.description);
          itemHtml = itemHtml.replace(/\{\{formatPrice price\}\}/g, (item.price || item.total).toFixed(2));
          itemHtml = itemHtml.replace(/\{\{quantity\}\}/g, item.quantity || 1);
          itemHtml = itemHtml.replace(/\{\{formatPrice total\}\}/g, item.total.toFixed(2));
          itemHtml = itemHtml.replace(/\{\{\.\.\/currency\}\}/g, invoiceData.currency);
          return itemHtml;
        }).join('');
        
        html = html.replace(itemsMatch[0], itemsHtml);
      }

      // Handle conditional sections
      html = html.replace(/\{\{#if tax\}\}([\s\S]*?)\{\{\/if\}\}/g, (match, content) => {
        return invoiceData.tax && invoiceData.tax > 0 ? content : '';
      });

      // Format prices
      html = html.replace(/\{\{formatPrice (\w+)\}\}/g, (match, field) => {
        const value = templateData[field];
        return value ? value.toFixed(2) : '0.00';
      });

      return html;
    } catch (error) {
      logger.error('Error generating HTML invoice:', error);
      throw error;
    }
  }

  /**
   * Get nested value from object using dot notation
   * @param {Object} obj - Object to search
   * @param {string} path - Dot notation path
   * @returns {*} Value or undefined
   */
  static getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => current && current[key], obj);
  }

  /**
   * Format date for display
   * @param {Date|string} date - Date to format
   * @returns {string} Formatted date
   */
  static formatDate(date) {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Convert HTML to PDF using Puppeteer
   * @param {string} html - HTML content
   * @param {Object} options - PDF options
   * @returns {Promise<Buffer>} PDF buffer
   */
  static async htmlToPdf(html, options = {}) {
    const puppeteer = await import('puppeteer');

    let browser;
    try {
      // Launch browser
      browser = await puppeteer.default.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
      });

      const page = await browser.newPage();

      // Set content with proper viewport
      await page.setContent(html, {
        waitUntil: 'networkidle0'
      });

      // Generate PDF with high quality settings
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        },
        preferCSSPageSize: true,
        ...options
      });

      return pdfBuffer;
    } catch (error) {
      logger.error('Error converting HTML to PDF with Puppeteer:', error);
      throw error;
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }

  /**
   * Generate complete invoice (HTML + PDF)
   * @param {Object} invoiceData - Invoice data
   * @returns {Promise<Object>} Generated invoice details
   */
  static async generateInvoice(invoiceData) {
    try {
      // Generate HTML
      const html = await this.generateHtmlInvoice(invoiceData);

      // Generate PDF from HTML
      const pdfBuffer = await this.htmlToPdf(html);

      const invoiceId = uuidv4();
      const pdfFilename = `invoice-${invoiceData.invoiceNumber}.pdf`;
      const htmlFilename = `invoice-${invoiceData.invoiceNumber}.html`;
      const pdfPath = path.join(this.invoicesDir, pdfFilename);
      const htmlPath = path.join(this.invoicesDir, htmlFilename);

      // Save both HTML and PDF
      await fs.writeFile(htmlPath, html, 'utf8');
      await fs.writeFile(pdfPath, pdfBuffer);

      // Store metadata
      const invoiceMetadata = {
        invoiceId,
        invoiceNumber: invoiceData.invoiceNumber,
        filename: pdfFilename,
        htmlFilename: htmlFilename,
        filePath: pdfPath,
        htmlPath: htmlPath,
        downloadUrl: `/api/invoices/${invoiceId}/download`,
        htmlUrl: `/api/invoices/${invoiceId}/html`,
        createdAt: new Date().toISOString(),
        type: 'pdf',
        customer: invoiceData.customer,
        total: invoiceData.total,
        currency: invoiceData.currency,
        storageType: 'local'
      };

      this.invoiceMetadata.set(invoiceId, invoiceMetadata);
      await this.saveMetadata();

      logger.info(`Invoice generated: ${pdfFilename} (HTML: ${htmlFilename})`);

      return invoiceMetadata;
    } catch (error) {
      logger.error('Error generating invoice:', error);
      throw error;
    }
  }

  /**
   * Get invoice by ID
   * @param {string} invoiceId - Invoice ID
   * @returns {Promise<Buffer>} Invoice file buffer
   */
  static async getInvoiceById(invoiceId) {
    try {
      const metadata = this.invoiceMetadata.get(invoiceId);
      if (!metadata) {
        throw new Error('Invoice not found');
      }

      return await fs.readFile(metadata.filePath);
    } catch (error) {
      logger.error('Error retrieving HTML invoice:', error);
      throw error;
    }
  }
}
